"""
Migration script to transfer existing memories from enhanced_memory_system to Graphiti
"""

import asyncio
import sqlite3
import json
import logging
from datetime import datetime
from typing import List, Dict, Any
import os
from dotenv import load_dotenv

from graphiti_memory_system import GraphitiMemorySystem

# Load environment variables
load_dotenv('local.env')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MemoryMigrator:
    """Migrates memories from SQLite enhanced memory system to Graphiti"""
    
    def __init__(self, sqlite_db_path: str = "enhanced_memories.db"):
        self.sqlite_db_path = sqlite_db_path
        self.graphiti_system = None
        
    async def initialize_graphiti(self):
        """Initialize Graphiti memory system"""
        
        neo4j_uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
        neo4j_user = os.getenv('NEO4J_USER', 'neo4j')
        neo4j_password = os.getenv('NEO4J_PASSWORD', 'password')
        
        self.graphiti_system = GraphitiMemorySystem(neo4j_uri, neo4j_user, neo4j_password)
        await self.graphiti_system.initialize()
        
        logger.info("Graphiti system initialized for migration")
    
    def read_existing_memories(self) -> List[Dict[str, Any]]:
        """Read memories from existing SQLite database"""
        
        if not os.path.exists(self.sqlite_db_path):
            logger.warning(f"SQLite database not found: {self.sqlite_db_path}")
            return []
        
        memories = []
        
        try:
            conn = sqlite3.connect(self.sqlite_db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, content, memory_type, importance_score, recency_score,
                       access_count, last_accessed, created_at, user_id, context, consolidated
                FROM enhanced_memories
                WHERE superseded_by IS NULL OR superseded_by = ''
                ORDER BY importance_score DESC, created_at DESC
            """)
            
            rows = cursor.fetchall()
            
            for row in rows:
                memory = {
                    'id': row[0],
                    'content': row[1],
                    'memory_type': row[2],
                    'importance_score': row[3],
                    'recency_score': row[4],
                    'access_count': row[5],
                    'last_accessed': row[6],
                    'created_at': row[7],
                    'user_id': row[8],
                    'context': json.loads(row[9]) if row[9] else {},
                    'consolidated': bool(row[10])
                }
                memories.append(memory)
            
            conn.close()
            logger.info(f"Read {len(memories)} memories from SQLite database")
            
        except Exception as e:
            logger.error(f"Error reading SQLite memories: {e}")
        
        return memories
    
    async def migrate_memories(self, memories: List[Dict[str, Any]]) -> Dict[str, int]:
        """Migrate memories to Graphiti"""
        
        if not self.graphiti_system:
            await self.initialize_graphiti()
        
        migration_stats = {
            'total': len(memories),
            'migrated': 0,
            'skipped': 0,
            'errors': 0
        }
        
        for memory in memories:
            try:
                # Map old memory types to new ones
                memory_type_mapping = {
                    'core_personality': 'core_personality',
                    'long_term_facts': 'facts',
                    'preferences': 'preferences',
                    'recent': 'interactions',
                    'working': 'interactions'
                }
                
                new_memory_type = memory_type_mapping.get(
                    memory['memory_type'], 
                    'facts'  # Default fallback
                )
                
                # Prepare context with migration info
                context = memory.get('context', {})
                context.update({
                    'migrated_from': 'enhanced_memory_system',
                    'original_id': memory['id'],
                    'original_type': memory['memory_type'],
                    'access_count': memory['access_count'],
                    'consolidated': memory['consolidated']
                })
                
                # Store in Graphiti
                await self.graphiti_system.store_memory(
                    content=memory['content'],
                    memory_type=new_memory_type,
                    user_id=memory['user_id'],
                    context=context,
                    importance_score=memory['importance_score']
                )
                
                migration_stats['migrated'] += 1
                
                if migration_stats['migrated'] % 10 == 0:
                    logger.info(f"Migrated {migration_stats['migrated']}/{migration_stats['total']} memories")
                
            except Exception as e:
                logger.error(f"Error migrating memory {memory['id']}: {e}")
                migration_stats['errors'] += 1
        
        return migration_stats
    
    async def verify_migration(self) -> Dict[str, Any]:
        """Verify the migration by checking Graphiti contents"""
        
        if not self.graphiti_system:
            await self.initialize_graphiti()
        
        try:
            # Get stats from Graphiti
            stats = await self.graphiti_system.get_memory_stats()
            
            # Test retrieval
            test_memories = await self.graphiti_system.retrieve_memories(
                query="Luna personality",
                max_results=5
            )
            
            verification = {
                'graphiti_stats': stats,
                'test_retrieval_count': len(test_memories),
                'sample_memories': [
                    {
                        'content': mem.content[:100] + '...' if len(mem.content) > 100 else mem.content,
                        'type': mem.memory_type,
                        'importance': mem.importance_score
                    }
                    for mem in test_memories[:3]
                ]
            }
            
            return verification
            
        except Exception as e:
            logger.error(f"Error verifying migration: {e}")
            return {'error': str(e)}
    
    async def close(self):
        """Close connections"""
        if self.graphiti_system:
            await self.graphiti_system.close()

async def main():
    """Main migration function"""
    
    logger.info("Starting memory migration from enhanced_memory_system to Graphiti")
    
    migrator = MemoryMigrator()
    
    try:
        # Read existing memories
        existing_memories = migrator.read_existing_memories()
        
        if not existing_memories:
            logger.info("No existing memories found to migrate")
            return
        
        # Initialize Graphiti
        await migrator.initialize_graphiti()
        
        # Perform migration
        logger.info(f"Starting migration of {len(existing_memories)} memories...")
        migration_stats = await migrator.migrate_memories(existing_memories)
        
        # Report results
        logger.info("Migration completed!")
        logger.info(f"Total memories: {migration_stats['total']}")
        logger.info(f"Successfully migrated: {migration_stats['migrated']}")
        logger.info(f"Errors: {migration_stats['errors']}")
        
        # Verify migration
        logger.info("Verifying migration...")
        verification = await migrator.verify_migration()
        logger.info(f"Verification results: {verification}")
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise
    
    finally:
        await migrator.close()

if __name__ == "__main__":
    asyncio.run(main())
