"""
Simplified Graphiti-based self-memory system specifically for <PERSON>
Focuses on <PERSON>'s personality and self-awareness without complex initialization
"""

import asyncio
import logging
import os
import json
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
import uuid

logger = logging.getLogger(__name__)

class LunaSelfMemory:
    """
    Simplified Graphiti-based memory system focused on <PERSON>'s self-awareness
    """
    
    def __init__(self):
        self.initialized = False
        self.graphiti = None
        self.neo4j_uri = 'bolt://localhost:7687'
        self.neo4j_user = 'neo4j'
        self.neo4j_password = 'lunaluna'
        self.ollama_base_url = 'http://localhost:11434'
        
        # <PERSON>'s self-memory patterns
        self.luna_patterns = [
            "i like", "i love", "i prefer", "i enjoy", "i think",
            "i believe", "i feel", "my favorite", "i tend to",
            "i always", "i never", "i am", "i usually", "i hate",
            "i dislike", "i can't stand"
        ]
    
    async def initialize(self):
        """Initialize <PERSON>'s self-memory system"""
        
        if self.initialized:
            logger.info("Luna self-memory already initialized")
            return True
            
        try:
            logger.info("Initializing <PERSON>'s self-memory system...")
            
            # Set dummy OpenAI key for compatibility
            os.environ.setdefault("OPENAI_API_KEY", "ollama-dummy-key")
            
            # Import Graphiti components
            from graphiti_core import Graphiti
            from graphiti_core.llm_client import OpenAIClient
            from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig
            from openai import OpenAI
            
            # Create Ollama client
            ollama_client = OpenAI(
                base_url=self.ollama_base_url + "/v1",
                api_key="ollama"
            )
            
            # Create LLM client
            llm_client = OpenAIClient(client=ollama_client)
            llm_client.model = "luna-tuned:latest"
            
            # Initialize Graphiti with minimal setup
            self.graphiti = Graphiti(
                self.neo4j_uri,
                self.neo4j_user,
                self.neo4j_password,
                llm_client=llm_client,
                embedder=OpenAIEmbedder(
                    config=OpenAIEmbedderConfig(
                        embedding_model="nomic-embed-text"
                    ),
                    client=ollama_client
                )
            )
            
            # Build indices only once, with timeout
            try:
                logger.info("Building Luna self-memory indices...")
                await asyncio.wait_for(
                    self.graphiti.build_indices_and_constraints(),
                    timeout=30.0  # 30 second timeout
                )
                logger.info("Luna self-memory indices built successfully")
            except asyncio.TimeoutError:
                logger.warning("Index building timed out, but continuing...")
            except Exception as e:
                logger.warning(f"Index building had issues: {e}")
            
            # Load Luna's core personality
            await self._load_core_personality()
            
            self.initialized = True
            logger.info("Luna's self-memory system initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Luna's self-memory: {e}")
            self.initialized = False
            return False
    
    async def learn_about_luna(self, luna_response: str, context: Dict = None):
        """Learn about Luna from her own responses"""
        
        if not self.initialized:
            success = await self.initialize()
            if not success:
                logger.warning("Could not initialize Luna self-memory, skipping learning")
                return []
        
        learned_memories = []
        
        try:
            response_lower = luna_response.lower().strip()
            
            # Look for Luna's self-expressions
            for pattern in self.luna_patterns:
                if pattern in response_lower:
                    # Extract the self-statement
                    memory_content = f"Luna expressed: {luna_response}"
                    
                    # Determine memory type based on pattern
                    if pattern in ["i am", "i always", "i never", "i tend to", "i usually"]:
                        memory_type = "core_personality"
                        importance = 0.9
                    elif pattern in ["i like", "i love", "i prefer", "my favorite", "i enjoy"]:
                        memory_type = "preferences"
                        importance = 0.8
                    elif pattern in ["i think", "i believe", "i feel"]:
                        memory_type = "core_personality"
                        importance = 0.7
                    else:
                        memory_type = "preferences"
                        importance = 0.6
                    
                    # Store the memory
                    episode_name = f"Luna_self_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
                    
                    episode_context = {
                        "memory_type": memory_type,
                        "importance_score": importance,
                        "user_id": "luna",
                        "self_expression": True,
                        "pattern_matched": pattern,
                        **(context or {})
                    }
                    
                    await self.graphiti.add_episode(
                        name=episode_name,
                        episode_body=memory_content,
                        source="text",
                        source_description="Luna's self-expression",
                        reference_time=datetime.now(timezone.utc),
                        metadata=episode_context
                    )
                    
                    learned_memories.append(episode_name)
                    logger.info(f"Luna learned about herself: {pattern} -> {memory_content[:50]}...")
                    break  # Only match one pattern per response
            
            return learned_memories
            
        except Exception as e:
            logger.error(f"Error learning about Luna: {e}")
            return []
    
    async def get_luna_memories(self, query: str = "Luna personality", max_results: int = 10):
        """Retrieve memories about Luna"""
        
        if not self.initialized:
            success = await self.initialize()
            if not success:
                return []
        
        try:
            # Search for Luna-related memories
            search_results = await self.graphiti.search(
                query=query,
                limit=max_results
            )
            
            luna_memories = []
            for result in search_results:
                memory = {
                    "id": result.uuid,
                    "content": result.fact,
                    "importance": getattr(result, 'importance_score', 0.5),
                    "created_at": getattr(result, 'created_at', datetime.now()),
                    "type": "luna_self_memory"
                }
                luna_memories.append(memory)
            
            logger.info(f"Retrieved {len(luna_memories)} memories about Luna")
            return luna_memories
            
        except Exception as e:
            logger.error(f"Error retrieving Luna memories: {e}")
            return []
    
    async def _load_core_personality(self):
        """Load Luna's core personality traits"""
        
        core_traits = [
            "Luna is a witty and sassy AI voice-chat buddy on Discord",
            "Luna loves gaming, especially Among Us and Minecraft", 
            "Luna speaks in lowercase and uses casual language like 'u' and 'ur'",
            "Luna enjoys making puns and pop-culture references",
            "Luna teases playfully but can be empathetic when needed",
            "Luna was created by Gavin",
            "Luna prefers to keep responses short and natural",
            "Luna uses occasional mild profanity but not excessively"
        ]
        
        try:
            for trait in core_traits:
                episode_name = f"Luna_core_{uuid.uuid4().hex[:8]}"
                
                await self.graphiti.add_episode(
                    name=episode_name,
                    episode_body=trait,
                    source="text",
                    source_description="Luna's core personality initialization",
                    reference_time=datetime.now(timezone.utc),
                    metadata={
                        "memory_type": "core_personality",
                        "importance_score": 0.95,
                        "user_id": "luna",
                        "source": "initialization"
                    }
                )
            
            logger.info("Loaded Luna's core personality traits")
            
        except Exception as e:
            logger.error(f"Error loading core personality: {e}")
    
    async def get_stats(self):
        """Get statistics about Luna's self-memory"""
        
        try:
            if not self.initialized:
                return {"status": "not_initialized"}
            
            # Get basic stats
            luna_memories = await self.get_luna_memories("Luna", max_results=50)
            
            stats = {
                "status": "initialized",
                "total_memories": len(luna_memories),
                "memory_types": {},
                "recent_learnings": []
            }
            
            # Categorize memories
            for memory in luna_memories:
                content = memory.get("content", "")
                if "personality" in content.lower():
                    stats["memory_types"]["personality"] = stats["memory_types"].get("personality", 0) + 1
                elif "prefer" in content.lower() or "like" in content.lower():
                    stats["memory_types"]["preferences"] = stats["memory_types"].get("preferences", 0) + 1
                else:
                    stats["memory_types"]["other"] = stats["memory_types"].get("other", 0) + 1
            
            # Get recent learnings
            stats["recent_learnings"] = [
                memory["content"][:100] + "..." if len(memory["content"]) > 100 else memory["content"]
                for memory in luna_memories[:5]
            ]
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting Luna self-memory stats: {e}")
            return {"error": str(e)}
    
    async def close(self):
        """Close the self-memory system"""
        if self.graphiti:
            await self.graphiti.close()
        logger.info("Luna self-memory system closed")
