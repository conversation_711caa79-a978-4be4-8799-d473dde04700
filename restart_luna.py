"""
Simple script to restart <PERSON> cleanly
"""

import subprocess
import sys
import time
import psutil

def kill_existing_luna():
    """Kill any existing Luna processes"""
    
    print("🔍 Looking for existing Luna processes...")
    
    killed_any = False
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            # Check if this is a Python process running main.py
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = proc.info['cmdline']
                if cmdline and any('main.py' in arg for arg in cmdline):
                    print(f"🔪 Killing Luna process (PID: {proc.info['pid']})")
                    proc.kill()
                    killed_any = True
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    if killed_any:
        print("⏳ Waiting for processes to terminate...")
        time.sleep(3)
        print("✅ Cleanup complete")
    else:
        print("✅ No existing Luna processes found")

def start_luna():
    """Start Luna"""
    
    print("🚀 Starting Luna with Graphiti memory system...")
    
    try:
        # Start Luna
        subprocess.run([sys.executable, "main.py"], check=True)
    except KeyboardInterrupt:
        print("\n⏹️ Luna stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Luna failed to start: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    print("🔄 Luna Restart Script")
    print("=" * 30)
    
    # Kill existing processes
    kill_existing_luna()
    
    # Start Luna
    start_luna()
