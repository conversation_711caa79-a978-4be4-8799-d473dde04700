"""
Diagnostic script to identify Graphiti initialization issues
"""

import asyncio
import logging
import os
import requests
from dotenv import load_dotenv

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

# Load environment variables
load_dotenv('local.env')

def check_ollama_connection():
    """Check if Ollama is running and accessible"""
    
    ollama_url = os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')
    
    try:
        print(f"🔍 Checking Ollama connection at {ollama_url}...")
        response = requests.get(f"{ollama_url}/api/tags", timeout=5)
        
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"   ✅ Ollama is running with {len(models)} models")
            
            # Check for required models
            model_names = [model['name'] for model in models]
            
            required_models = [
                'nomic-embed-text',
                'hf.co/lmstudio-community/Qwen3-8B-GGUF:Q4_K_M'
            ]
            
            for model in required_models:
                if any(model in name for name in model_names):
                    print(f"   ✅ Found model: {model}")
                else:
                    print(f"   ❌ Missing model: {model}")
                    print(f"      Run: ollama pull {model}")
            
            return True
        else:
            print(f"   ❌ Ollama returned status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Cannot connect to Ollama: {e}")
        print("   Make sure Ollama is running: ollama serve")
        return False

def check_neo4j_connection():
    """Check Neo4j connection"""
    
    try:
        from neo4j import GraphDatabase
        
        uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
        user = os.getenv('NEO4J_USER', 'neo4j')
        password = os.getenv('NEO4J_PASSWORD', 'lunaluna')
        
        print(f"🔍 Checking Neo4j connection at {uri}...")
        
        driver = GraphDatabase.driver(uri, auth=(user, password))
        with driver.session() as session:
            result = session.run("RETURN 1 as test")
            test_value = result.single()["test"]
            
            if test_value == 1:
                print("   ✅ Neo4j connection working")
                return True
        
        driver.close()
        
    except Exception as e:
        print(f"   ❌ Neo4j connection failed: {e}")
        return False

async def test_simple_graphiti():
    """Test Graphiti with minimal functionality"""
    
    try:
        print("🔍 Testing basic Graphiti initialization...")
        
        # Try importing and creating a simple instance
        from graphiti_core import Graphiti
        
        neo4j_uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
        neo4j_user = os.getenv('NEO4J_USER', 'neo4j')
        neo4j_password = os.getenv('NEO4J_PASSWORD', 'lunaluna')
        
        # Create basic Graphiti instance without custom clients
        graphiti = Graphiti(neo4j_uri, neo4j_user, neo4j_password)
        
        print("   ✅ Graphiti instance created")
        
        # Try to build indices (this might be where it's hanging)
        print("   🔍 Building indices and constraints...")
        await graphiti.build_indices_and_constraints()
        print("   ✅ Indices built successfully")
        
        await graphiti.close()
        print("   ✅ Graphiti closed successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Graphiti test failed: {e}")
        return False

async def main():
    """Run all diagnostics"""
    
    print("🩺 Graphiti Diagnostic Tool")
    print("=" * 50)
    
    # Check dependencies
    print("\n1. Checking Ollama...")
    ollama_ok = check_ollama_connection()
    
    print("\n2. Checking Neo4j...")
    neo4j_ok = check_neo4j_connection()
    
    print("\n3. Testing basic Graphiti...")
    graphiti_ok = await test_simple_graphiti()
    
    # Summary
    print("\n" + "=" * 50)
    print("DIAGNOSTIC SUMMARY")
    print("=" * 50)
    
    print(f"Ollama:   {'✅ OK' if ollama_ok else '❌ FAIL'}")
    print(f"Neo4j:    {'✅ OK' if neo4j_ok else '❌ FAIL'}")
    print(f"Graphiti: {'✅ OK' if graphiti_ok else '❌ FAIL'}")
    
    if all([ollama_ok, neo4j_ok, graphiti_ok]):
        print("\n🎉 All systems working! Graphiti should be ready.")
        print("\nTo integrate with Luna:")
        print("1. The integration is already coded in main.py")
        print("2. Just run Luna normally: python main.py")
        print("3. Luna will use Graphiti for memory instead of the old system")
    else:
        print("\n❌ Issues found. Please fix the failing components.")
        
        if not ollama_ok:
            print("\nOllama fixes:")
            print("- Make sure Ollama is running: ollama serve")
            print("- Install required models:")
            print("  ollama pull nomic-embed-text")
            print("  ollama pull hf.co/lmstudio-community/Qwen3-8B-GGUF:Q4_K_M")
        
        if not neo4j_ok:
            print("\nNeo4j fixes:")
            print("- Make sure Neo4j Desktop is running")
            print("- Check credentials in local.env")
        
        if not graphiti_ok:
            print("\nGraphiti fixes:")
            print("- Check the error message above")
            print("- May need to restart Neo4j or Ollama")

if __name__ == "__main__":
    asyncio.run(main())
