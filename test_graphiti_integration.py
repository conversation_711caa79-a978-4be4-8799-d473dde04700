"""
Test script for Graphiti memory system integration
Verifies that the system works correctly before deployment
"""

import asyncio
import logging
import os
from dotenv import load_dotenv
from graphiti_memory_system import GraphitiMemorySystem
from graphiti_memory_integration import LunaGraphitiIntegration

# Load environment variables
load_dotenv('local.env')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_basic_functionality():
    """Test basic Graphiti memory system functionality"""

    logger.info("Testing basic Graphiti functionality...")

    # Initialize system
    neo4j_uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.getenv('NEO4J_USER', 'Luna')
    neo4j_password = os.getenv('NEO4J_PASSWORD', 'lunaluna')
    ollama_base_url = os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')

    graphiti_system = GraphitiMemorySystem(neo4j_uri, neo4j_user, neo4j_password, ollama_base_url)

    try:
        # Test initialization
        await graphiti_system.initialize()
        logger.info("✅ Graphiti system initialized successfully")

        # Test storing memories
        test_memories = [
            {
                "content": "User John likes playing Minecraft and building castles",
                "memory_type": "preferences",
                "user_id": "test_user_123",
                "importance_score": 0.8
            },
            {
                "content": "Luna enjoys helping users with creative projects",
                "memory_type": "core_personality",
                "importance_score": 0.9
            },
            {
                "content": "The user mentioned they work as a software developer",
                "memory_type": "facts",
                "user_id": "test_user_123",
                "importance_score": 0.7
            }
        ]

        stored_ids = []
        for memory in test_memories:
            memory_id = await graphiti_system.store_memory(**memory)
            stored_ids.append(memory_id)
            logger.info(f"✅ Stored memory: {memory_id}")

        # Test retrieving memories
        logger.info("Testing memory retrieval...")

        # Test 1: General query
        memories = await graphiti_system.retrieve_memories(
            query="What does the user like?",
            max_results=5
        )
        logger.info(f"✅ Retrieved {len(memories)} memories for general query")

        # Test 2: User-specific query
        user_memories = await graphiti_system.retrieve_memories(
            query="user preferences",
            user_id="test_user_123",
            max_results=5
        )
        logger.info(f"✅ Retrieved {len(user_memories)} user-specific memories")

        # Test 3: Learning from conversation
        test_conversation = [
            {"role": "user", "content": "I really love playing chess"},
            {"role": "assistant", "content": "That's great! Chess is a wonderful strategic game."},
            {"role": "user", "content": "I've been playing for about 5 years now"}
        ]

        learned_memories = await graphiti_system.learn_from_conversation(
            messages=test_conversation,
            user_id="test_user_123",
            conversation_type="dm"
        )
        logger.info(f"✅ Learned {len(learned_memories)} memories from conversation")

        # Test memory stats
        stats = await graphiti_system.get_memory_stats()
        logger.info(f"✅ Memory system stats: {stats}")

        return True

    except Exception as e:
        logger.error(f"❌ Basic functionality test failed: {e}")
        return False

    finally:
        await graphiti_system.close()

async def test_integration_layer():
    """Test the Luna integration layer"""

    logger.info("Testing Luna integration layer...")

    # Initialize systems
    neo4j_uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.getenv('NEO4J_USER', 'Luna')
    neo4j_password = os.getenv('NEO4J_PASSWORD', 'lunaluna')
    ollama_base_url = os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')

    graphiti_system = GraphitiMemorySystem(neo4j_uri, neo4j_user, neo4j_password, ollama_base_url)
    integration = LunaGraphitiIntegration(graphiti_system)

    try:
        await graphiti_system.initialize()

        # Test DM memory retrieval
        dm_memories = await integration.retrieve_for_conversation(
            query="What games does the user like?",
            is_dm=True,
            user_id="test_user_123",
            max_memories=8
        )
        logger.info(f"✅ DM retrieval returned {len(dm_memories)} memories")

        # Test group memory retrieval
        group_memories = await integration.retrieve_for_conversation(
            query="Luna's personality",
            is_dm=False,
            user_id="test_user_123",
            max_memories=15
        )
        logger.info(f"✅ Group retrieval returned {len(group_memories)} memories")

        # Test explicit memory storage
        explicit_memory_id = await integration.store_explicit_memory(
            content="User prefers dark mode interfaces",
            memory_type="preferences",
            user_id="test_user_123",
            importance_score=0.6
        )
        logger.info(f"✅ Stored explicit memory: {explicit_memory_id}")

        # Test conversation learning
        test_conversation = [
            {"role": "user", "content": "I'm working on a Python project"},
            {"role": "assistant", "content": "Python is great! What kind of project?"},
            {"role": "user", "content": "It's a web scraper using BeautifulSoup"}
        ]

        learned = await integration.learn_from_conversation(
            messages=test_conversation,
            user_id="test_user_123",
            context={"is_dm": True}
        )
        logger.info(f"✅ Integration learning returned {len(learned) if learned else 0} memories")

        # Test memory summary
        summary = await integration.get_memory_summary(user_id="test_user_123")
        logger.info(f"✅ Memory summary: {summary}")

        return True

    except Exception as e:
        logger.error(f"❌ Integration layer test failed: {e}")
        return False

    finally:
        await integration.close()

async def test_error_handling():
    """Test error handling and edge cases"""

    logger.info("Testing error handling...")

    # Test with invalid Neo4j credentials
    try:
        invalid_system = GraphitiMemorySystem(
            "bolt://localhost:7687",
            "invalid_user",
            "invalid_password",
            "http://localhost:11434"
        )
        await invalid_system.initialize()
        logger.error("❌ Should have failed with invalid credentials")
        return False
    except Exception:
        logger.info("✅ Correctly handled invalid Neo4j credentials")

    # Test with empty queries
    neo4j_uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.getenv('NEO4J_USER', 'Luna')
    neo4j_password = os.getenv('NEO4J_PASSWORD', 'lunaluna')
    ollama_base_url = os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')

    graphiti_system = GraphitiMemorySystem(neo4j_uri, neo4j_user, neo4j_password, ollama_base_url)

    try:
        await graphiti_system.initialize()

        # Test empty query
        memories = await graphiti_system.retrieve_memories(
            query="",
            max_results=5
        )
        logger.info(f"✅ Handled empty query, returned {len(memories)} memories")

        # Test very long content
        long_content = "This is a very long memory content. " * 100
        memory_id = await graphiti_system.store_memory(
            content=long_content,
            memory_type="facts",
            importance_score=0.5
        )
        logger.info(f"✅ Handled long content: {memory_id}")

        return True

    except Exception as e:
        logger.error(f"❌ Error handling test failed: {e}")
        return False

    finally:
        await graphiti_system.close()

async def main():
    """Run all tests"""

    logger.info("🧪 Starting Graphiti integration tests...")

    tests = [
        ("Basic Functionality", test_basic_functionality),
        ("Integration Layer", test_integration_layer),
        ("Error Handling", test_error_handling)
    ]

    results = []

    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")

        try:
            result = await test_func()
            results.append((test_name, result))

            if result:
                logger.info(f"✅ {test_name} PASSED")
            else:
                logger.error(f"❌ {test_name} FAILED")

        except Exception as e:
            logger.error(f"❌ {test_name} CRASHED: {e}")
            results.append((test_name, False))

    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")

    logger.info(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        logger.info("🎉 All tests passed! Graphiti integration is ready.")
        return True
    else:
        logger.error("❌ Some tests failed. Please check the logs and fix issues.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
