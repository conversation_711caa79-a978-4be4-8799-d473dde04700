"""
Quick test to verify luna-tuned:latest model works with Ollama
"""

import requests
import json

def test_luna_model():
    """Test if luna-tuned:latest model is working"""
    
    print("🧪 Testing luna-tuned:latest model with Ollama...")
    
    try:
        # Test basic Ollama connection
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code != 200:
            print("❌ Ollama is not running or accessible")
            return False
        
        models = response.json().get('models', [])
        model_names = [model['name'] for model in models]
        
        # Check if luna-tuned model exists
        luna_model_found = any('luna-tuned' in name for name in model_names)
        if not luna_model_found:
            print("❌ luna-tuned model not found in Ollama")
            print(f"Available models: {model_names}")
            return False
        
        print("✅ luna-tuned model found in Ollama")
        
        # Test the model with a simple prompt
        test_payload = {
            "model": "luna-tuned:latest",
            "prompt": "Hello! Please respond briefly.",
            "stream": False
        }
        
        print("🔍 Testing model response...")
        response = requests.post(
            "http://localhost:11434/api/generate",
            json=test_payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            response_text = result.get('response', '').strip()
            
            if response_text:
                print(f"✅ Model responded: {response_text[:100]}...")
                return True
            else:
                print("❌ Model returned empty response")
                return False
        else:
            print(f"❌ Model request failed with status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_embedding_model():
    """Test if nomic-embed-text model is working"""
    
    print("\n🧪 Testing nomic-embed-text embedding model...")
    
    try:
        # Test embedding model
        test_payload = {
            "model": "nomic-embed-text",
            "prompt": "This is a test sentence for embedding."
        }
        
        response = requests.post(
            "http://localhost:11434/api/embeddings",
            json=test_payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            embedding = result.get('embedding', [])
            
            if embedding and len(embedding) > 0:
                print(f"✅ Embedding model working (dimension: {len(embedding)})")
                return True
            else:
                print("❌ Embedding model returned empty result")
                return False
        else:
            print(f"❌ Embedding request failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Embedding test error: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Testing Ollama Models for Graphiti")
    print("=" * 50)
    
    luna_ok = test_luna_model()
    embed_ok = test_embedding_model()
    
    print("\n" + "=" * 50)
    print("TEST RESULTS")
    print("=" * 50)
    print(f"luna-tuned:latest:  {'✅ OK' if luna_ok else '❌ FAIL'}")
    print(f"nomic-embed-text:   {'✅ OK' if embed_ok else '❌ FAIL'}")
    
    if luna_ok and embed_ok:
        print("\n🎉 All models working! Graphiti should work correctly.")
    else:
        print("\n❌ Some models failed. Please check:")
        if not luna_ok:
            print("- Make sure luna-tuned:latest is pulled: ollama pull luna-tuned:latest")
        if not embed_ok:
            print("- Make sure nomic-embed-text is pulled: ollama pull nomic-embed-text")
