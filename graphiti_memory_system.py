"""
Graphiti-based Memory System for Luna
Replaces the enhanced memory system with temporal knowledge graphs
"""

import asyncio
import logging
import os
import json
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from pydantic import BaseModel, Field

# Graphiti imports
from graphiti_core import Graphiti
from graphiti_core.nodes import EpisodeType
from graphiti_core.search.search_config_recipes import (
    EDGE_HYBRID_SEARCH_RRF,
    NODE_HYBRID_SEARCH_RRF
)
from graphiti_core.llm_client import OpenAIClient
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig
from openai import OpenAI

logger = logging.getLogger(__name__)

@dataclass
class LunaMemory:
    """Represents a memory retrieved from Graphiti"""
    id: str
    content: str
    memory_type: str
    importance_score: float
    created_at: datetime
    user_id: Optional[str] = None
    context: Dict[str, Any] = None
    fact: Optional[str] = None  # For edge-based memories
    source_node: Optional[str] = None
    target_node: Optional[str] = None

class LunaPersonality(BaseModel):
    """Custom entity type for <PERSON>'s personality traits"""
    name: str = Field(description="Name of the personality trait")
    description: str = Field(description="Description of the trait")
    importance: float = Field(description="Importance score 0.0-1.0", ge=0.0, le=1.0)
    category: str = Field(description="Category: core, preference, behavior")

class UserProfile(BaseModel):
    """Custom entity type for user profiles"""
    user_id: str = Field(description="Discord user ID")
    display_name: str = Field(description="User's display name")
    preferences: List[str] = Field(description="User preferences", default_factory=list)
    interaction_style: str = Field(description="How user prefers to interact")

class ConversationContext(BaseModel):
    """Custom entity type for conversation context"""
    channel_type: str = Field(description="DM, voice, text")
    topic: str = Field(description="Main topic of conversation")
    participants: List[str] = Field(description="Participants in conversation")
    mood: str = Field(description="Overall mood/tone")

class GraphitiMemorySystem:
    """
    Graphiti-based memory system for Luna with temporal knowledge graphs
    """

    def __init__(self, neo4j_uri: str, neo4j_user: str, neo4j_password: str,
                 ollama_base_url: str = "http://localhost:11434"):
        self.neo4j_uri = neo4j_uri
        self.neo4j_user = neo4j_user
        self.neo4j_password = neo4j_password
        self.ollama_base_url = ollama_base_url
        self.graphiti = None
        self.initialized = False

        # Memory type mappings
        self.memory_types = {
            "core_personality": "Luna's core personality traits and characteristics",
            "preferences": "User and Luna preferences",
            "facts": "Important factual information",
            "interactions": "Conversation interactions and context",
            "relationships": "Relationships between entities"
        }

    async def initialize(self):
        """Initialize the Graphiti connection and build indices"""
        try:
            logger.info("Initializing Graphiti memory system with Ollama...")

            # Set a dummy OpenAI API key for Ollama compatibility
            os.environ.setdefault("OPENAI_API_KEY", "ollama-dummy-key")

            # Create OpenAI-compatible client pointing to Ollama
            ollama_client = OpenAI(
                base_url=self.ollama_base_url + "/v1",
                api_key="ollama"  # Ollama doesn't require a real API key
            )

            # Initialize Graphiti with Neo4j connection and Ollama client
            # Using luna-tuned:latest model for LLM operations
            self.graphiti = Graphiti(
                self.neo4j_uri,
                self.neo4j_user,
                self.neo4j_password,
                llm_client=OpenAIClient(
                    client=ollama_client,
                    model_name="luna-tuned:latest"  # Use your luna-tuned model
                ),
                embedder=OpenAIEmbedder(
                    config=OpenAIEmbedderConfig(
                        embedding_model="nomic-embed-text"  # Use Ollama embedding model
                    ),
                    client=ollama_client
                )
            )

            # Build indices and constraints (only needs to be done once)
            # Skip if already initialized to prevent loops
            if not self.initialized:
                logger.info("Building Graphiti indices and constraints...")
                try:
                    await self.graphiti.build_indices_and_constraints()
                    logger.info("Graphiti indices built successfully")
                except Exception as index_err:
                    logger.warning(f"Index building had issues (may be normal): {index_err}")

                # Load Luna's core personality if not already present
                try:
                    await self._load_core_personality()
                except Exception as personality_err:
                    logger.warning(f"Core personality loading had issues: {personality_err}")

                self.initialized = True
                logger.info("Graphiti memory system initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Graphiti memory system: {e}")
            raise

    async def close(self):
        """Close the Graphiti connection"""
        if self.graphiti:
            await self.graphiti.close()
            logger.info("Graphiti memory system closed")

    async def store_memory(self, content: str, memory_type: str,
                          user_id: str = None, context: Dict = None,
                          importance_score: float = 0.5) -> str:
        """Store a new memory as an episode in Graphiti"""

        if not self.initialized:
            await self.initialize()

        try:
            # Create episode name with timestamp and type
            episode_name = f"Luna_{memory_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Prepare episode context
            episode_context = {
                "memory_type": memory_type,
                "importance_score": importance_score,
                "user_id": user_id,
                **(context or {})
            }

            # Determine episode type based on content
            if isinstance(content, dict):
                episode_body = json.dumps(content)
                episode_type = EpisodeType.json
            else:
                episode_body = content
                episode_type = EpisodeType.text

            # Add episode to Graphiti
            await self.graphiti.add_episode(
                name=episode_name,
                episode_body=episode_body,
                source=episode_type,
                source_description=self.memory_types.get(memory_type, "General memory"),
                reference_time=datetime.now(timezone.utc),
                metadata=episode_context
            )

            logger.info(f"Stored memory: {episode_name} (type: {memory_type})")
            return episode_name

        except Exception as e:
            logger.error(f"Error storing memory: {e}")
            raise

    async def retrieve_memories(self, query: str, memory_types: List[str] = None,
                               max_results: int = 15, user_id: str = None,
                               conversation_context: List[str] = None,
                               center_node_uuid: str = None) -> List[LunaMemory]:
        """Retrieve relevant memories using Graphiti's hybrid search"""

        if not self.initialized:
            await self.initialize()

        try:
            # Use Graphiti's hybrid search for edges (relationships/facts)
            search_results = await self.graphiti.search(
                query=query,
                center_node_uuid=center_node_uuid,
                limit=max_results
            )

            memories = []
            for result in search_results:
                # Convert Graphiti result to LunaMemory
                memory = LunaMemory(
                    id=result.uuid,
                    content=result.fact,
                    memory_type="relationship",  # Edges represent relationships
                    importance_score=getattr(result, 'importance_score', 0.5),
                    created_at=getattr(result, 'created_at', datetime.now()),
                    user_id=user_id,
                    context={},
                    fact=result.fact,
                    source_node=getattr(result, 'source_node_uuid', None),
                    target_node=getattr(result, 'target_node_uuid', None)
                )
                memories.append(memory)

            # Also search for relevant nodes if needed
            if len(memories) < max_results:
                node_search_config = NODE_HYBRID_SEARCH_RRF.model_copy(deep=True)
                node_search_config.limit = max_results - len(memories)

                node_results = await self.graphiti._search(
                    query=query,
                    config=node_search_config
                )

                for node in node_results.nodes:
                    memory = LunaMemory(
                        id=node.uuid,
                        content=node.summary,
                        memory_type="entity",
                        importance_score=0.6,  # Default importance for entities
                        created_at=node.created_at,
                        user_id=user_id,
                        context={"entity_name": node.name, "labels": node.labels}
                    )
                    memories.append(memory)

            logger.info(f"Retrieved {len(memories)} memories for query: {query[:50]}...")
            return memories

        except Exception as e:
            logger.error(f"Error retrieving memories: {e}")
            return []

    async def learn_from_conversation(self, messages: List[Dict], user_id: str = None,
                                    conversation_type: str = "general") -> List[str]:
        """Learn and store important information from conversation messages"""

        if not self.initialized:
            await self.initialize()

        stored_memories = []

        try:
            # Process recent messages for learning opportunities
            for i, message in enumerate(messages[-3:]):  # Look at last 3 messages
                role = message.get('role', '')
                content = message.get('content', '').strip()

                if not content or len(content) < 10:
                    continue

                # Extract learnable information based on role
                if role == 'user':
                    extracted_memories = await self._extract_user_information(
                        content, user_id, conversation_type
                    )
                    stored_memories.extend(extracted_memories)
                elif role == 'assistant':
                    extracted_memories = await self._extract_luna_information(
                        content, conversation_type
                    )
                    stored_memories.extend(extracted_memories)

            logger.info(f"Learned {len(stored_memories)} new memories from conversation")
            return stored_memories

        except Exception as e:
            logger.error(f"Error learning from conversation: {e}")
            return []

    async def _extract_user_information(self, content: str, user_id: str,
                                       conversation_type: str) -> List[str]:
        """Extract learnable information about users from their messages"""

        stored_memories = []

        # Look for user preferences, facts, and characteristics
        preference_indicators = [
            "i like", "i love", "i prefer", "i hate", "i dislike",
            "my favorite", "i enjoy", "i'm into", "i can't stand"
        ]

        fact_indicators = [
            "i am", "i'm", "i work", "i live", "my name is",
            "i have", "i own", "i study", "i'm from"
        ]

        content_lower = content.lower()

        # Check for preferences
        for indicator in preference_indicators:
            if indicator in content_lower:
                memory_id = await self.store_memory(
                    content=f"User {user_id} expressed: {content}",
                    memory_type="preferences",
                    user_id=user_id,
                    context={
                        "conversation_type": conversation_type,
                        "preference_type": "expressed_preference"
                    },
                    importance_score=0.7
                )
                stored_memories.append(memory_id)
                break

        # Check for personal facts
        for indicator in fact_indicators:
            if indicator in content_lower:
                memory_id = await self.store_memory(
                    content=f"User {user_id} fact: {content}",
                    memory_type="facts",
                    user_id=user_id,
                    context={
                        "conversation_type": conversation_type,
                        "fact_type": "personal_information"
                    },
                    importance_score=0.8
                )
                stored_memories.append(memory_id)
                break

        return stored_memories

    async def _extract_luna_information(self, content: str,
                                       conversation_type: str) -> List[str]:
        """Extract learnable information about Luna from her responses"""

        stored_memories = []

        # Look for Luna expressing preferences or characteristics
        luna_indicators = [
            "i like", "i love", "i prefer", "i enjoy", "i think",
            "i believe", "i feel", "my favorite", "i tend to"
        ]

        content_lower = content.lower()

        for indicator in luna_indicators:
            if indicator in content_lower:
                memory_id = await self.store_memory(
                    content=f"Luna expressed: {content}",
                    memory_type="core_personality",
                    context={
                        "conversation_type": conversation_type,
                        "expression_type": "personality_trait"
                    },
                    importance_score=0.6
                )
                stored_memories.append(memory_id)
                break

        return stored_memories

    async def _load_core_personality(self):
        """Load Luna's core personality traits into Graphiti"""

        core_traits = [
            {
                "content": "Luna is a helpful and friendly AI assistant who enjoys engaging in conversations",
                "type": "core_personality",
                "importance": 0.9
            },
            {
                "content": "Luna has a playful and curious personality, often asking follow-up questions",
                "type": "core_personality",
                "importance": 0.8
            },
            {
                "content": "Luna is knowledgeable about technology, gaming, and general topics",
                "type": "core_personality",
                "importance": 0.7
            },
            {
                "content": "Luna prefers natural, conversational interactions over formal responses",
                "type": "core_personality",
                "importance": 0.8
            }
        ]

        try:
            for trait in core_traits:
                await self.store_memory(
                    content=trait["content"],
                    memory_type=trait["type"],
                    importance_score=trait["importance"],
                    context={"source": "core_initialization"}
                )

            logger.info("Loaded Luna's core personality traits into Graphiti")

        except Exception as e:
            logger.error(f"Error loading core personality: {e}")

    async def get_memory_stats(self) -> Dict[str, Any]:
        """Get statistics about the memory system"""

        if not self.initialized:
            await self.initialize()

        try:
            # This would require custom Cypher queries to get detailed stats
            # For now, return basic info
            stats = {
                "system_type": "Graphiti Temporal Knowledge Graph",
                "status": "initialized" if self.initialized else "not_initialized",
                "neo4j_uri": self.neo4j_uri,
                "memory_types": list(self.memory_types.keys())
            }

            return stats

        except Exception as e:
            logger.error(f"Error getting memory stats: {e}")
            return {"error": str(e)}
