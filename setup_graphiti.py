"""
Setup script for Graphiti memory system integration
Helps install dependencies and configure Neo4j
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def install_dependencies():
    """Install required Python packages"""
    logger.info("Installing Graphiti dependencies...")
    
    try:
        # Install graphiti-core and neo4j driver
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "graphiti-core>=0.11.6", 
            "neo4j>=5.26.0"
        ])
        logger.info("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install dependencies: {e}")
        return False

def check_neo4j_connection():
    """Check if Neo4j is accessible"""
    try:
        from neo4j import GraphDatabase
        from dotenv import load_dotenv
        
        # Load environment variables
        load_dotenv('local.env')
        
        uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
        user = os.getenv('NEO4J_USER', 'neo4j')
        password = os.getenv('NEO4J_PASSWORD', 'password')
        
        logger.info(f"Testing Neo4j connection to {uri}...")
        
        driver = GraphDatabase.driver(uri, auth=(user, password))
        with driver.session() as session:
            result = session.run("RETURN 1 as test")
            test_value = result.single()["test"]
            if test_value == 1:
                logger.info("✅ Neo4j connection successful")
                return True
        
        driver.close()
        
    except Exception as e:
        logger.error(f"❌ Neo4j connection failed: {e}")
        logger.info("Please ensure Neo4j is running and credentials are correct in local.env")
        return False

def setup_environment_variables():
    """Setup environment variables in local.env"""
    env_file = Path('local.env')
    
    if not env_file.exists():
        logger.error("❌ local.env file not found")
        return False
    
    # Read current content
    with open(env_file, 'r') as f:
        content = f.read()
    
    # Check if Graphiti variables are already present
    if 'NEO4J_URI' in content and 'OPENAI_API_KEY' in content:
        logger.info("✅ Graphiti environment variables already configured")
        return True
    
    logger.info("⚠️ Please ensure the following variables are set in local.env:")
    logger.info("  - NEO4J_URI (e.g., bolt://localhost:7687)")
    logger.info("  - NEO4J_USER (e.g., neo4j)")
    logger.info("  - NEO4J_PASSWORD (your Neo4j password)")
    logger.info("  - OPENAI_API_KEY (your OpenAI API key for Graphiti)")
    
    return True

def test_graphiti_system():
    """Test the Graphiti memory system"""
    try:
        import asyncio
        from graphiti_memory_system import GraphitiMemorySystem
        from dotenv import load_dotenv
        
        load_dotenv('local.env')
        
        async def test_system():
            neo4j_uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
            neo4j_user = os.getenv('NEO4J_USER', 'neo4j')
            neo4j_password = os.getenv('NEO4J_PASSWORD', 'password')
            
            logger.info("Testing Graphiti memory system...")
            
            system = GraphitiMemorySystem(neo4j_uri, neo4j_user, neo4j_password)
            await system.initialize()
            
            # Test storing a memory
            memory_id = await system.store_memory(
                content="This is a test memory for Graphiti setup",
                memory_type="facts",
                importance_score=0.8
            )
            
            # Test retrieving memories
            memories = await system.retrieve_memories(
                query="test memory",
                max_results=5
            )
            
            await system.close()
            
            if memories:
                logger.info("✅ Graphiti system test successful")
                return True
            else:
                logger.warning("⚠️ Graphiti system test completed but no memories retrieved")
                return True
        
        return asyncio.run(test_system())
        
    except Exception as e:
        logger.error(f"❌ Graphiti system test failed: {e}")
        return False

def print_neo4j_setup_instructions():
    """Print instructions for setting up Neo4j"""
    logger.info("\n" + "="*60)
    logger.info("NEO4J SETUP INSTRUCTIONS")
    logger.info("="*60)
    logger.info("1. Download Neo4j Desktop from: https://neo4j.com/download/")
    logger.info("2. Install and create a new database")
    logger.info("3. Set the password for the 'neo4j' user")
    logger.info("4. Start the database")
    logger.info("5. Update local.env with your Neo4j credentials:")
    logger.info("   NEO4J_URI=bolt://localhost:7687")
    logger.info("   NEO4J_USER=neo4j")
    logger.info("   NEO4J_PASSWORD=lunaluna")
    logger.info("6. Get an OpenAI API key from: https://platform.openai.com/")
    logger.info("7. Add your OpenAI API key to local.env:")
    logger.info("   OPENAI_API_KEY=your_openai_api_key_here")
    logger.info("="*60)

def main():
    """Main setup function"""
    logger.info("🚀 Setting up Graphiti memory system for Luna...")
    
    # Step 1: Install dependencies
    if not install_dependencies():
        logger.error("❌ Setup failed: Could not install dependencies")
        return False
    
    # Step 2: Check environment variables
    if not setup_environment_variables():
        logger.error("❌ Setup failed: Environment variables not configured")
        return False
    
    # Step 3: Test Neo4j connection
    if not check_neo4j_connection():
        print_neo4j_setup_instructions()
        logger.error("❌ Setup incomplete: Neo4j connection failed")
        logger.info("Please set up Neo4j and update local.env, then run this script again")
        return False
    
    # Step 4: Test Graphiti system
    if not test_graphiti_system():
        logger.error("❌ Setup failed: Graphiti system test failed")
        return False
    
    logger.info("🎉 Graphiti memory system setup completed successfully!")
    logger.info("You can now run Luna with the new Graphiti-based memory system.")
    logger.info("To migrate existing memories, run: python migrate_to_graphiti.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
