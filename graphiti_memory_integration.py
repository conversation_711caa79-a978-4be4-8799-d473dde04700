"""
Graphiti Memory Integration for <PERSON>
Replaces memory_integration.py with Graphiti-based temporal knowledge graphs
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from graphiti_memory_system import GraphitiMemorySystem, LunaMemory

logger = logging.getLogger(__name__)

class LunaGraphitiIntegration:
    """
    Integration layer between <PERSON>'s conversation system and Graphiti memory
    """
    
    def __init__(self, graphiti_memory: GraphitiMemorySystem, existing_rag_retriever=None):
        self.graphiti_memory = graphiti_memory
        self.existing_rag = existing_rag_retriever
        self.memory_learning_enabled = True
        
    async def retrieve_for_conversation(self, query: str, is_dm: bool = False,
                                      user_id: str = None, max_memories: int = 15,
                                      conversation_context: List[str] = None) -> List[Dict]:
        """
        Main retrieval method that uses Graphiti for memory retrieval
        """
        
        if is_dm:
            # For DMs, focus on personalized memories and user-specific context
            return await self._retrieve_dm_memories(query, user_id, max_memories, conversation_context)
        else:
            # For voice/guild channels, use broader context with smart filtering
            return await self._retrieve_group_memories(query, user_id, max_memories, conversation_context)
    
    async def _retrieve_dm_memories(self, query: str, user_id: str, max_memories: int,
                                   conversation_context: List[str] = None) -> List[Dict]:
        """Retrieve memories optimized for DM conversations"""
        
        try:
            # Use Graphiti's temporal search with user context
            memories = await self.graphiti_memory.retrieve_memories(
                query=query,
                memory_types=["core_personality", "preferences", "facts"],
                max_results=max_memories,
                user_id=user_id,
                conversation_context=conversation_context
            )
            
            # Convert to format expected by Luna's response system
            formatted_memories = []
            for memory in memories:
                formatted_memory = {
                    "content": memory.content,
                    "metadata": {
                        "memory_type": memory.memory_type,
                        "importance_score": memory.importance_score,
                        "created_at": memory.created_at.isoformat(),
                        "user_id": memory.user_id,
                        "source": "graphiti_dm"
                    }
                }
                
                # Add fact information if available (for relationship memories)
                if memory.fact:
                    formatted_memory["fact"] = memory.fact
                    formatted_memory["metadata"]["source_node"] = memory.source_node
                    formatted_memory["metadata"]["target_node"] = memory.target_node
                
                formatted_memories.append(formatted_memory)
            
            logger.info(f"Retrieved {len(formatted_memories)} DM memories for user {user_id}")
            return formatted_memories
            
        except Exception as e:
            logger.error(f"Error retrieving DM memories: {e}")
            return []
    
    async def _retrieve_group_memories(self, query: str, user_id: str, max_memories: int,
                                      conversation_context: List[str] = None) -> List[Dict]:
        """Retrieve memories optimized for group conversations"""
        
        try:
            # For group conversations, focus more on general knowledge and less on personal info
            memories = await self.graphiti_memory.retrieve_memories(
                query=query,
                memory_types=["core_personality", "facts", "interactions"],
                max_results=max_memories,
                user_id=user_id,
                conversation_context=conversation_context
            )
            
            # Convert to format expected by Luna's response system
            formatted_memories = []
            for memory in memories:
                formatted_memory = {
                    "content": memory.content,
                    "metadata": {
                        "memory_type": memory.memory_type,
                        "importance_score": memory.importance_score,
                        "created_at": memory.created_at.isoformat(),
                        "source": "graphiti_group"
                    }
                }
                
                # Add fact information if available
                if memory.fact:
                    formatted_memory["fact"] = memory.fact
                
                formatted_memories.append(formatted_memory)
            
            # Optionally supplement with RAG if we have fewer memories than requested
            if self.existing_rag and len(formatted_memories) < max_memories:
                try:
                    rag_count = max_memories - len(formatted_memories)
                    rag_results = await asyncio.to_thread(
                        self.existing_rag.retrieve, query, top_k=rag_count
                    )
                    
                    for rag_result in rag_results:
                        rag_memory = {
                            "content": rag_result.get("content", ""),
                            "metadata": {
                                "memory_type": "rag_supplement",
                                "importance_score": 0.4,  # Lower importance for RAG
                                "source": "rag_fallback"
                            }
                        }
                        formatted_memories.append(rag_memory)
                    
                    logger.info(f"Supplemented with {len(rag_results)} RAG memories")
                    
                except Exception as rag_err:
                    logger.warning(f"RAG supplement failed: {rag_err}")
            
            logger.info(f"Retrieved {len(formatted_memories)} group memories")
            return formatted_memories
            
        except Exception as e:
            logger.error(f"Error retrieving group memories: {e}")
            return []
    
    async def learn_from_conversation(self, messages: List[Dict], user_id: str = None,
                                    context: Dict = None):
        """
        Learn and store important information from conversation
        """
        
        if not self.memory_learning_enabled:
            return
        
        try:
            # Determine conversation type from context
            conversation_type = "general"
            if context:
                if context.get("is_dm"):
                    conversation_type = "dm"
                elif context.get("is_voice"):
                    conversation_type = "voice"
                elif context.get("channel_type"):
                    conversation_type = context["channel_type"]
            
            # Use Graphiti's learning system
            learned_memories = await self.graphiti_memory.learn_from_conversation(
                messages=messages,
                user_id=user_id,
                conversation_type=conversation_type
            )
            
            if learned_memories:
                logger.info(f"Learned {len(learned_memories)} new memories from conversation")
            
            return learned_memories
            
        except Exception as e:
            logger.error(f"Error learning from conversation: {e}")
            return []
    
    async def store_explicit_memory(self, content: str, memory_type: str = "facts",
                                   user_id: str = None, importance_score: float = 0.7) -> str:
        """
        Store an explicit memory (e.g., from user command)
        """
        
        try:
            memory_id = await self.graphiti_memory.store_memory(
                content=content,
                memory_type=memory_type,
                user_id=user_id,
                importance_score=importance_score,
                context={"source": "explicit_command"}
            )
            
            logger.info(f"Stored explicit memory: {memory_id}")
            return memory_id
            
        except Exception as e:
            logger.error(f"Error storing explicit memory: {e}")
            raise
    
    async def get_memory_summary(self, user_id: str = None) -> Dict[str, Any]:
        """
        Get a summary of stored memories for a user or in general
        """
        
        try:
            # Get basic stats from Graphiti
            stats = await self.graphiti_memory.get_memory_stats()
            
            # Add user-specific information if requested
            if user_id:
                user_memories = await self.graphiti_memory.retrieve_memories(
                    query=f"user {user_id}",
                    max_results=50,
                    user_id=user_id
                )
                
                stats["user_memory_count"] = len(user_memories)
                stats["user_id"] = user_id
                
                # Categorize memories by type
                memory_types = {}
                for memory in user_memories:
                    mem_type = memory.memory_type
                    memory_types[mem_type] = memory_types.get(mem_type, 0) + 1
                
                stats["user_memory_types"] = memory_types
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting memory summary: {e}")
            return {"error": str(e)}
    
    async def close(self):
        """Close the memory system connections"""
        if self.graphiti_memory:
            await self.graphiti_memory.close()
        logger.info("Graphiti memory integration closed")
