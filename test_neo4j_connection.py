"""
Simple Neo4j connection tester to debug authentication issues
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv('local.env')

def test_neo4j_connection():
    """Test Neo4j connection with detailed error reporting"""

    try:
        from neo4j import GraphDatabase

        # Get credentials from environment
        uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
        user = os.getenv('NEO4J_USER', 'Luna')
        password = os.getenv('NEO4J_PASSWORD', 'lunaluna')

        print(f"Testing connection to: {uri}")
        print(f"Username: {user}")
        print(f"Password: {'*' * len(password)}")
        print("-" * 50)

        # Try to connect
        driver = GraphDatabase.driver(uri, auth=(user, password))

        # Test the connection
        with driver.session() as session:
            result = session.run("RETURN 1 as test")
            test_value = result.single()["test"]

            if test_value == 1:
                print("✅ SUCCESS: Neo4j connection working!")

                # Get some database info
                result = session.run("CALL db.info()")
                info = result.single()
                print(f"Database: {info.get('name', 'Unknown')}")

                # Test write permissions
                try:
                    session.run("CREATE (n:TestNode {created: datetime()}) RETURN n")
                    session.run("MATCH (n:TestNode) DELETE n")
                    print("✅ Write permissions: OK")
                except Exception as write_err:
                    print(f"⚠️ Write permissions: {write_err}")

                return True

        driver.close()

    except Exception as e:
        print(f"❌ Connection failed: {e}")
        print("\nTroubleshooting steps:")
        print("1. Check if Neo4j Desktop is running")
        print("2. Verify the database is started (green play button)")
        print("3. Check connection details in Neo4j Desktop")
        print("4. Try connecting through Neo4j Browser first")
        print("5. Reset password if needed")
        return False

def suggest_common_fixes():
    """Suggest common fixes for Neo4j connection issues"""

    print("\n" + "="*60)
    print("COMMON NEO4J CONNECTION ISSUES & FIXES")
    print("="*60)

    print("\n1. DEFAULT CREDENTIALS:")
    print("   Try these common defaults:")
    print("   NEO4J_USER=neo4j")
    print("   NEO4J_PASSWORD=neo4j")
    print("   (or whatever you set during setup)")

    print("\n2. CHECK NEO4J DESKTOP:")
    print("   - Open Neo4j Desktop")
    print("   - Make sure your database has a green 'Start' button")
    print("   - Click the database name to see connection details")

    print("\n3. ALTERNATIVE PORTS:")
    print("   Sometimes Neo4j uses different ports:")
    print("   NEO4J_URI=bolt://localhost:7687  (default)")
    print("   NEO4J_URI=bolt://localhost:7688  (alternative)")
    print("   NEO4J_URI=neo4j://localhost:7687 (newer versions)")

    print("\n4. RESET PASSWORD:")
    print("   In Neo4j Desktop:")
    print("   - Stop the database")
    print("   - Click '...' menu → Reset Password")
    print("   - Start the database again")

    print("\n5. BROWSER TEST:")
    print("   Test in Neo4j Browser first:")
    print("   - Open http://localhost:7474")
    print("   - Try connecting with your credentials")

    print("="*60)

if __name__ == "__main__":
    print("🔍 Testing Neo4j Connection...")
    print("="*50)

    success = test_neo4j_connection()

    if not success:
        suggest_common_fixes()

        print(f"\n📝 Current environment variables:")
        print(f"NEO4J_URI={os.getenv('NEO4J_URI', 'NOT SET')}")
        print(f"NEO4J_USER={os.getenv('NEO4J_USER', 'NOT SET')}")
        print(f"NEO4J_PASSWORD={'SET' if os.getenv('NEO4J_PASSWORD') else 'NOT SET'}")
