"""
Quick test to verify <PERSON><PERSON><PERSON><PERSON> is working without verbose logging
"""

import asyncio
import logging
import os
from dotenv import load_dotenv

# Reduce logging verbosity
logging.getLogger("neo4j.notifications").setLevel(logging.WARNING)
logging.getLogger("graphiti_core").setLevel(logging.WARNING)
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

from graphiti_memory_system import GraphitiMemorySystem

# Load environment variables
load_dotenv('local.env')

async def quick_test():
    """Quick test of Graphiti functionality"""
    
    print("🧪 Quick Graphiti Test")
    print("=" * 40)
    
    # Initialize system
    neo4j_uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.getenv('NEO4J_USER', 'neo4j')
    neo4j_password = os.getenv('NEO4J_PASSWORD', 'lunaluna')
    ollama_base_url = os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')
    
    system = GraphitiMemorySystem(neo4j_uri, neo4j_user, neo4j_password, ollama_base_url)
    
    try:
        print("1. Initializing Graphiti...")
        await system.initialize()
        print("   ✅ Initialization successful!")
        
        print("\n2. Storing a test memory...")
        memory_id = await system.store_memory(
            content="User enjoys playing video games, especially RPGs",
            memory_type="preferences",
            user_id="test_user_123",
            importance_score=0.8
        )
        print(f"   ✅ Memory stored: {memory_id}")
        
        print("\n3. Retrieving memories...")
        memories = await system.retrieve_memories(
            query="What does the user like?",
            max_results=5
        )
        print(f"   ✅ Retrieved {len(memories)} memories")
        
        if memories:
            print("   📝 Sample memory:")
            print(f"      Content: {memories[0].content}")
            print(f"      Type: {memories[0].memory_type}")
            print(f"      Importance: {memories[0].importance_score}")
        
        print("\n4. Testing conversation learning...")
        test_conversation = [
            {"role": "user", "content": "I love playing chess on weekends"},
            {"role": "assistant", "content": "Chess is a great strategic game!"}
        ]
        
        learned = await system.learn_from_conversation(
            messages=test_conversation,
            user_id="test_user_123",
            conversation_type="dm"
        )
        print(f"   ✅ Learned {len(learned)} memories from conversation")
        
        print("\n🎉 All tests passed! Graphiti is working correctly.")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return False
    
    finally:
        await system.close()

if __name__ == "__main__":
    success = asyncio.run(quick_test())
    
    if success:
        print("\n" + "=" * 40)
        print("✅ GRAPHITI IS READY!")
        print("You can now run Luna with the new memory system.")
        print("The verbose logging you saw earlier is normal.")
    else:
        print("\n" + "=" * 40)
        print("❌ There are still issues to resolve.")
