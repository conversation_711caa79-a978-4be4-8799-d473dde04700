# Graphiti Memory System Integration for Luna

This document describes the integration of Graphiti temporal knowledge graphs into <PERSON>'s memory system, replacing the previous enhanced memory system with a more sophisticated and scalable solution.

## What is Graphiti?

Graphiti is a framework for building and querying temporally-aware knowledge graphs, specifically designed for AI agents. Unlike traditional RAG approaches, Graphiti:

- **Builds dynamic, temporal knowledge graphs** from both structured and unstructured data
- **Handles real-time incremental updates** without requiring batch recomputation  
- **Provides hybrid search capabilities** combining semantic, keyword (BM25), and graph traversal
- **Tracks temporal relationships** with bi-temporal data model
- **Handles contradictions** through temporal edge invalidation

## Why Replace the Enhanced Memory System?

<PERSON>'s previous memory system had several issues:

- **Echo problems**: Retrieved memories often contained the same topics being discussed
- **Repetitive responses**: Poor memory retrieval led to repetitive conversations
- **Inconsistency issues**: The multi-tier system showed consistency problems
- **Poor relevance**: Memory retrieval wasn't contextually relevant enough

Graphiti addresses these issues with:

- **Better relevance**: Hybrid semantic + graph-based search
- **Temporal awareness**: Tracks how relationships change over time
- **No echo problems**: Sophisticated retrieval prevents repetitive content
- **Scalability**: Designed for large-scale knowledge management

## Architecture Overview

### New Components

1. **`graphiti_memory_system.py`**: Core Graphiti integration
2. **`graphiti_memory_integration.py`**: Integration layer for Luna
3. **`migrate_to_graphiti.py`**: Migration script from old system
4. **`setup_graphiti.py`**: Setup and installation helper

### Dependencies

- **Neo4j**: Graph database backend (5.26+)
- **graphiti-core**: Core Graphiti framework (0.11.6+)
- **OpenAI API**: For LLM inference and embeddings

## Setup Instructions

### 1. Install Dependencies

Run the setup script:
```bash
python setup_graphiti.py
```

Or manually install:
```bash
pip install graphiti-core>=0.11.6 neo4j>=5.26.0
```

### 2. Set Up Neo4j

1. Download [Neo4j Desktop](https://neo4j.com/download/)
2. Create a new database
3. Set password for 'neo4j' user
4. Start the database

### 3. Configure Environment Variables

Add to `local.env`:
```env
# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_neo4j_password

# OpenAI API for Graphiti
OPENAI_API_KEY=your_openai_api_key
```

### 4. Migrate Existing Memories (Optional)

If you have existing memories in the enhanced memory system:
```bash
python migrate_to_graphiti.py
```

## How It Works

### Memory Storage

Memories are stored as **episodes** in Graphiti:

```python
# Example: Store a user preference
await graphiti_memory.store_memory(
    content="User likes playing Minecraft",
    memory_type="preferences", 
    user_id="123456789",
    importance_score=0.7
)
```

### Memory Retrieval

Graphiti uses hybrid search combining:
- **Semantic similarity**: Vector embeddings
- **Keyword search**: BM25 full-text search  
- **Graph traversal**: Relationship-based retrieval

```python
# Example: Retrieve relevant memories
memories = await graphiti_memory.retrieve_memories(
    query="What games does the user like?",
    max_results=10,
    user_id="123456789"
)
```

### Automatic Learning

The system automatically learns from conversations:

```python
# Learns from recent messages
learned_memories = await memory_integration.learn_from_conversation(
    messages=recent_messages,
    user_id=user_id,
    context={"is_dm": True}
)
```

## Memory Types

The system categorizes memories into types:

- **`core_personality`**: Luna's fundamental traits and characteristics
- **`preferences`**: User and Luna preferences  
- **`facts`**: Important factual information
- **`interactions`**: Conversation interactions and context
- **`relationships`**: Relationships between entities

## Integration Points

### Main Bot (`main.py`)

- Initializes Graphiti memory system on startup
- Replaces enhanced memory system initialization
- Handles proper cleanup on shutdown

### Response Processing (`llm_response/processing.py`)

- Uses Graphiti for memory retrieval during conversations
- Automatically learns from conversations
- Maintains backward compatibility with RAG fallback

### Memory Integration (`graphiti_memory_integration.py`)

- Provides unified interface for memory operations
- Handles DM vs group conversation differences
- Manages learning and retrieval strategies

## Benefits Over Previous System

### 1. Better Memory Relevance
- Hybrid search finds more contextually relevant memories
- Graph relationships provide better context understanding
- Temporal awareness tracks how information changes

### 2. Reduced Echo Problems  
- Sophisticated retrieval prevents repetitive content
- Temporal invalidation handles contradictions
- Better diversity in retrieved memories

### 3. Improved Scalability
- Neo4j backend handles large datasets efficiently
- Incremental updates without full recomputation
- Parallel processing capabilities

### 4. Enhanced Temporal Awareness
- Tracks when relationships form and change
- Point-in-time queries for historical context
- Bi-temporal data model (event time vs ingestion time)

## Monitoring and Debugging

### Memory Statistics

Get system statistics:
```python
stats = await memory_integration.get_memory_summary(user_id="123456789")
print(stats)
```

### Neo4j Browser

Access Neo4j Browser at `http://localhost:7474` to:
- Visualize the knowledge graph
- Run Cypher queries
- Monitor database performance

### Logging

The system provides detailed logging:
- Memory storage operations
- Retrieval queries and results  
- Learning from conversations
- Error handling and fallbacks

## Troubleshooting

### Common Issues

1. **Neo4j Connection Failed**
   - Ensure Neo4j is running
   - Check credentials in `local.env`
   - Verify firewall settings

2. **OpenAI API Errors**
   - Check API key validity
   - Monitor usage limits
   - Verify network connectivity

3. **Memory Retrieval Issues**
   - Check Neo4j database status
   - Review query logs
   - Verify memory storage

### Performance Tuning

- Adjust `max_results` in retrieval calls
- Monitor Neo4j memory usage
- Consider Neo4j performance tuning for large datasets

## Migration Notes

### From Enhanced Memory System

The migration script (`migrate_to_graphiti.py`) handles:
- Reading existing SQLite memories
- Converting to Graphiti episodes
- Mapping memory types
- Preserving importance scores and metadata

### Backward Compatibility

- RAG system remains as fallback
- Existing conversation logs preserved
- Gradual transition possible

## Future Enhancements

Potential improvements:
- Custom entity types for specific domains
- Advanced graph algorithms for memory consolidation
- Integration with external knowledge sources
- Performance optimizations for large-scale deployments

## Support

For issues or questions:
1. Check the logs for error messages
2. Verify Neo4j and OpenAI connectivity
3. Review the Graphiti documentation
4. Check the Discord for community support
